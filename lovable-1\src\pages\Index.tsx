
import { useEffect, useState } from 'react';
import { ChevronDown, Play, Download, Users, Leaf, TrendingUp, Star, Mail, Phone, MapPin, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

const Index = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const services = [
    {
      icon: <Leaf className="w-12 h-12 text-[#2D4D31]" />,
      title: "Smart Farming Solutions",
      description: "Advanced agricultural technology to optimize crop yield and reduce waste."
    },
    {
      icon: <TrendingUp className="w-12 h-12 text-[#2D4D31]" />,
      title: "Market Intelligence",
      description: "Real-time market data and analytics to make informed trading decisions."
    },
    {
      icon: <Users className="w-12 h-12 text-[#2D4D31]" />,
      title: "Supply Chain Network",
      description: "Connect directly with farmers, traders, and manufacturers globally."
    }
  ];

  const testimonials = [
    {
      name: "John Farmer",
      role: "Organic Farmer",
      content: "AgriTram has revolutionized how I connect with buyers. The platform is intuitive and the results are amazing.",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Sarah Chen",
      role: "Agricultural Trader",
      content: "The transparency and efficiency AgriTram brings to agricultural trading is unprecedented.",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face"
    },
    {
      name: "Mike Rodriguez",
      role: "Food Manufacturer",
      content: "Finding quality suppliers has never been easier. AgriTram streamlined our entire procurement process.",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
    }
  ];

  const whitepapers = [
    {
      title: "The Future of Agricultural Trading",
      description: "Comprehensive analysis of blockchain technology in agriculture",
      downloadUrl: "#"
    },
    {
      title: "Sustainable Farming Practices",
      description: "Research on environmental impact and sustainability",
      downloadUrl: "#"
    },
    {
      title: "Market Dynamics Report 2024",
      description: "Current trends and future predictions in agricultural markets",
      downloadUrl: "#"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-[#2D4D31]/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-[#2D4D31]">AgriTram</span>
            </div>
            
            {/* Desktop Menu */}
            <div className="hidden md:flex items-center space-x-8">
              <a href="#hero" className="text-[#2D4D31] hover:text-[#2D4D31]/80 transition-colors">Home</a>
              <a href="#about" className="text-[#2D4D31] hover:text-[#2D4D31]/80 transition-colors">About</a>
              <a href="#services" className="text-[#2D4D31] hover:text-[#2D4D31]/80 transition-colors">Services</a>
              <a href="#contact" className="text-[#2D4D31] hover:text-[#2D4D31]/80 transition-colors">Contact</a>
              <Button className="bg-[#2D4D31] hover:bg-[#2D4D31]/90 text-white border-2 border-[#2D4D31]/70 border-dotted">
                Get Started
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <button onClick={() => setIsMenuOpen(!isMenuOpen)} className="text-[#2D4D31]">
                {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>

          {/* Mobile Menu */}
          {isMenuOpen && (
            <div className="md:hidden bg-white border-t border-[#2D4D31]/20">
              <div className="px-2 pt-2 pb-3 space-y-1">
                <a href="#hero" className="block px-3 py-2 text-[#2D4D31] hover:bg-[#FFF5EA]">Home</a>
                <a href="#about" className="block px-3 py-2 text-[#2D4D31] hover:bg-[#FFF5EA]">About</a>
                <a href="#services" className="block px-3 py-2 text-[#2D4D31] hover:bg-[#FFF5EA]">Services</a>
                <a href="#contact" className="block px-3 py-2 text-[#2D4D31] hover:bg-[#FFF5EA]">Contact</a>
                <Button className="w-full mt-2 bg-[#2D4D31] hover:bg-[#2D4D31]/90 text-white border-2 border-[#2D4D31]/70 border-dotted">
                  Get Started
                </Button>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section id="hero" className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div 
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1506744038136-46273834b3fb?ixlib=rb-4.0.3)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            transform: `translateY(${scrollY * 0.5}px)`,
          }}
        >
          <div className="absolute inset-0 bg-black/40"></div>
        </div>
        
        
      </section>

      {/* Coming Soon Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl sm:text-5xl font-bold text-[#2D4D31] mb-6 animate-fade-in-up">
              Coming Soon
            </h2>
            <p className="text-xl text-gray-600 mb-8 animate-fade-in-up">
              We're putting the finishing touches on something extraordinary. Join our waitlist to be the first to experience the future of agricultural trading.
            </p>
            <div className="bg-[#FFF5EA] p-8 rounded-2xl animate-fade-in-up">
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input 
                  type="email" 
                  placeholder="Enter your email" 
                  className="flex-1 px-4 py-3 border border-[#2D4D31]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D4D31]/50"
                />
                <Button className="bg-[#2D4D31] hover:bg-[#2D4D31]/90 text-white border-2 border-[#2D4D31]/70 border-dotted px-6 py-3">
                  Join Waitlist
                </Button>
              </div>
              <p className="text-sm text-gray-500 mt-4">Be among the first 1000 users and get exclusive early access benefits</p>
            </div>
          </div>
        </div>
      </section>

      {/* Video Section */}
      <section className="relative py-20 overflow-hidden">
        <div 
          className="absolute inset-0 z-0"
          style={{
            backgroundImage: 'url(https://images.unsplash.com/photo-1501854140801-50d01698950b?ixlib=rb-4.0.3)',
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            transform: `translateY(${scrollY * 0.3}px)`,
          }}
        >
          <div className="absolute inset-0 bg-[#2D4D31]/80"></div>
        </div>

        <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-white mb-6 animate-fade-in-up text-shadow">
            Our Vision
          </h2>
          <p className="text-xl text-white/90 mb-12 max-w-3xl mx-auto animate-fade-in-up text-shadow">
            Watch how AgriTram is transforming the agricultural industry through innovation, sustainability, and technology.
          </p>
          
          <div className="relative max-w-4xl mx-auto animate-fade-in-up">
            <div className="relative bg-black/20 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <div className="aspect-video bg-black/40 rounded-lg flex items-center justify-center">
                <button className="bg-white/20 backdrop-blur-sm rounded-full p-6 hover:bg-white/30 transition-colors group">
                  <Play className="w-12 h-12 text-white group-hover:scale-110 transition-transform" />
                </button>
              </div>
              <p className="text-white/80 mt-4 text-lg">Click to watch our mission video</p>
            </div>
          </div>
        </div>
      </section>

      {/* White Paper Section */}
      <section className="py-20 bg-[#FFF5EA]">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-[#2D4D31] mb-6 animate-fade-in-up">
              Research & Insights
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-fade-in-up">
              Dive deep into our research and discover the data-driven insights shaping the future of agriculture.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {whitepapers.map((paper, index) => (
              <Card key={index} className="bg-white border-[#2D4D31]/20 hover:shadow-lg transition-shadow animate-fade-in-up">
                <CardContent className="p-6">
                  <div className="bg-[#FFF5EA] w-16 h-16 rounded-lg flex items-center justify-center mb-4">
                    <Download className="w-8 h-8 text-[#2D4D31]" />
                  </div>
                  <h3 className="text-xl font-semibold text-[#2D4D31] mb-3">{paper.title}</h3>
                  <p className="text-gray-600 mb-4">{paper.description}</p>
                  <Button variant="outline" className="w-full border-[#2D4D31] text-[#2D4D31] hover:bg-[#2D4D31] hover:text-white">
                    Download PDF
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-[#2D4D31] mb-6 animate-fade-in-up">
              Our Solutions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-fade-in-up">
              Comprehensive solutions designed to empower every stakeholder in the agricultural ecosystem.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="text-center p-8 border-[#2D4D31]/20 hover:shadow-lg transition-all hover:scale-105 animate-fade-in-up">
                <CardContent className="p-0">
                  <div className="mb-6 flex justify-center animate-float">
                    {service.icon}
                  </div>
                  <h3 className="text-2xl font-semibold text-[#2D4D31] mb-4">{service.title}</h3>
                  <p className="text-gray-600">{service.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* About Us Section */}
      <section id="about" className="py-20 bg-[#FFF5EA]">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <div className="animate-slide-in-left">
              <h2 className="text-4xl sm:text-5xl font-bold text-[#2D4D31] mb-6">
                About AgriTram
              </h2>
              <p className="text-xl text-gray-600 mb-6">
                We're a team of agricultural experts, technology innovators, and sustainability advocates working together to create a more efficient and transparent agricultural marketplace.
              </p>
              <p className="text-gray-600 mb-8">
                Our platform leverages blockchain technology, AI-driven market insights, and a global network of verified partners to connect every participant in the agricultural value chain.
              </p>
              <Button className="bg-[#2D4D31] hover:bg-[#2D4D31]/90 text-white border-2 border-[#2D4D31]/70 border-dotted">
                Meet Our Team
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 animate-slide-in-right">
              <div className="bg-white p-6 rounded-lg text-center">
                <div className="text-3xl font-bold text-[#2D4D31] mb-2">50+</div>
                <div className="text-gray-600">Countries Served</div>
              </div>
              <div className="bg-white p-6 rounded-lg text-center">
                <div className="text-3xl font-bold text-[#2D4D31] mb-2">10k+</div>
                <div className="text-gray-600">Active Farmers</div>
              </div>
              <div className="bg-white p-6 rounded-lg text-center">
                <div className="text-3xl font-bold text-[#2D4D31] mb-2">$500M+</div>
                <div className="text-gray-600">Transactions</div>
              </div>
              <div className="bg-white p-6 rounded-lg text-center">
                <div className="text-3xl font-bold text-[#2D4D31] mb-2">99.9%</div>
                <div className="text-gray-600">Uptime</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-[#2D4D31] mb-6 animate-fade-in-up">
              What Our Users Say
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-fade-in-up">
              Real stories from real users who are transforming their agricultural businesses with AgriTram.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="border-[#2D4D31]/20 hover:shadow-lg transition-shadow animate-fade-in-up">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-6 italic">"{testimonial.content}"</p>
                  <div className="flex items-center">
                    <img 
                      src={testimonial.avatar} 
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full mr-4"
                    />
                    <div>
                      <div className="font-semibold text-[#2D4D31]">{testimonial.name}</div>
                      <div className="text-sm text-gray-500">{testimonial.role}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-[#2D4D31] text-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl sm:text-5xl font-bold mb-6 animate-fade-in-up">
            Ready to Transform Your Agricultural Business?
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto animate-fade-in-up opacity-90">
            Join thousands of farmers, traders, and manufacturers who are already benefiting from our revolutionary platform.
          </p>
          <div className="space-y-4 sm:space-y-0 sm:space-x-4 sm:flex sm:justify-center animate-fade-in-up">
            <Button size="lg" className="bg-white text-[#2D4D31] hover:bg-gray-100 border-2 border-white/70 border-dotted text-lg px-8 py-4">
              Start Your Free Trial
            </Button>
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-[#2D4D31] text-lg px-8 py-4">
              Schedule a Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-[#2D4D31] mb-6 animate-fade-in-up">
              Get in Touch
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-fade-in-up">
              Have questions? We'd love to hear from you. Send us a message and we'll respond as soon as possible.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-16">
            <div className="animate-slide-in-left">
              <form className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                  <input 
                    type="text" 
                    className="w-full px-4 py-3 border border-[#2D4D31]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D4D31]/50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input 
                    type="email" 
                    className="w-full px-4 py-3 border border-[#2D4D31]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D4D31]/50"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                  <textarea 
                    rows={6}
                    className="w-full px-4 py-3 border border-[#2D4D31]/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D4D31]/50"
                  ></textarea>
                </div>
                <Button className="w-full bg-[#2D4D31] hover:bg-[#2D4D31]/90 text-white border-2 border-[#2D4D31]/70 border-dotted py-3">
                  Send Message
                </Button>
              </form>
            </div>

            <div className="animate-slide-in-right">
              <div className="space-y-8">
                <div className="flex items-start space-x-4">
                  <div className="bg-[#FFF5EA] w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Mail className="w-6 h-6 text-[#2D4D31]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-[#2D4D31] mb-2">Email Us</h3>
                    <p className="text-gray-600"><EMAIL></p>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-[#FFF5EA] w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Phone className="w-6 h-6 text-[#2D4D31]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-[#2D4D31] mb-2">Call Us</h3>
                    <p className="text-gray-600">+****************</p>
                    <p className="text-gray-600">Mon-Fri 9am-6pm EST</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-[#FFF5EA] w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
                    <MapPin className="w-6 h-6 text-[#2D4D31]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-[#2D4D31] mb-2">Visit Us</h3>
                    <p className="text-gray-600">123 Agriculture Street</p>
                    <p className="text-gray-600">Farm City, FC 12345</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-[#2D4D31]/20 py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="text-2xl font-bold text-[#2D4D31] mb-4">AgriTram</div>
              <p className="text-gray-600 mb-4">Revolutionizing agricultural trading through technology and innovation.</p>
              <div className="flex space-x-4">
                <a href="#" className="text-[#2D4D31] hover:text-[#2D4D31]/80">Facebook</a>
                <a href="#" className="text-[#2D4D31] hover:text-[#2D4D31]/80">Twitter</a>
                <a href="#" className="text-[#2D4D31] hover:text-[#2D4D31]/80">LinkedIn</a>
              </div>
            </div>
            
            <div>
              <h3 className="font-semibold text-[#2D4D31] mb-4">Product</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Features</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Pricing</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">API</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Documentation</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-[#2D4D31] mb-4">Company</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">About</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Blog</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Careers</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Press</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-[#2D4D31] mb-4">Support</h3>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Help Center</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Contact</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Privacy</a></li>
                <li><a href="#" className="text-gray-600 hover:text-[#2D4D31]">Terms</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-[#2D4D31]/20 mt-8 pt-8 text-center">
            <p className="text-gray-600">&copy; 2024 AgriTram. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
