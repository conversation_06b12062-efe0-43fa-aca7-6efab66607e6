import React, { useEffect, useState } from 'react';
import './Hero.css';

const Hero: React.FC = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => {
      setScrollY(window.scrollY);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const parallaxOffset = scrollY * 0.5;

  return (
    <section className="hero section--hero" id="hero">
      <div 
        className="hero__background parallax"
        style={{
          transform: `translateY(${parallaxOffset}px)`,
        }}
      >
        <div className="hero__overlay"></div>
      </div>
      
      
    </section>
  );
};

export default Hero;
